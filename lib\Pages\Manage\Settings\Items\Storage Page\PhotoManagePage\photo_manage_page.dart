import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:facelog/Pages/Manage/Settings/Items/Storage%20Page/PhotoManagePage/photo_item.dart';
import 'package:facelog/Pages/Manage/Settings/Items/Storage%20Page/PhotoManagePage/photo_manager_provider.dart';
import 'package:facelog/Pages/Manage/Settings/Items/Storage%20Page/PhotoManagePage/photo_analysis_loading_widget.dart';
import 'package:facelog/Pages/Produce/CameraPreview/face_detection.dart';
import 'package:facelog/Pages/Produce/CameraPreview/photo_statistics_container.dart';
import 'package:facelog/core/mixins/memory_management_mixin.dart';
import 'package:facelog/core/mixins/disk_cache_mixin.dart';
import 'package:facelog/product/config/language/locale_keys.g.dart';
import 'package:facelog/product/enums/status_enum.dart';
import 'package:facelog/product/models/Photo/Photo.dart';
import 'package:facelog/product/services/helper.dart';
import 'package:facelog/product/services/log_service.dart';

// Photo Management için ayrı progress değişkenleri
late int photoAnalysisAllPhotosCount;
ValueNotifier<int> photoAnalysisProgressIndex = ValueNotifier<int>(0);

class PhotoManagePage extends StatefulWidget {
  const PhotoManagePage({
    super.key,
  });

  @override
  State<PhotoManagePage> createState() => _PhotoManagePageState();
}

class _PhotoManagePageState extends State<PhotoManagePage> with MemoryManagementMixin, DiskCacheMixin {
  // Texts
  final String _title = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_Title.tr();
  final String _noDateWarning = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_NoDateWarning.tr();
  final String _undatedPhotosSet = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_UndatedPhotosSet.tr();
  final String _mustAnalyze = LocaleKeys.SettingsPage_SettingsTiles_Storage_Body_PhotoManager_YouMustAnalyze.tr();

  bool isLoadingPhotos = false;

  late final photoManagerProviderRead = context.read<PhotoManagerProvider>();

  @override
  void initState() {
    super.initState();

    LogService().logScreen("PhotoManagePage");
  }

  @override
  void dispose() {
    isLoadingPhotos = false;
    // Cache temizliği asenkron olarak yap
    clearPhotoCache();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isSelectedAnyPhotos = context.watch<PhotoManagerProvider>().selectedPhotos.isNotEmpty;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          await leavePage();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            padding: const EdgeInsets.only(left: 10),
            icon: const Icon(
              Icons.arrow_back_ios,
            ),
            onPressed: () async {
              await leavePage();
            },
          ),
          title: Text(_title),
          actions: [
            if (isSelectedAnyPhotos) ...[
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () {
                  photoManagerProviderRead.deleteSelectedPhotos();
                },
              ),
              IconButton(
                icon: const Icon(Icons.date_range),
                onPressed: () {
                  photoManagerProviderRead.changeDate(context);
                },
              ),
            ],
          ],
        ),
        body: Stack(
          children: [
            GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 0.79,
              ),
              shrinkWrap: true,
              padding: const EdgeInsets.all(2),
              itemCount: photoManagerProviderRead.importedPhotos.length,
              itemBuilder: (BuildContext context, int index) {
                return PhotoItem(photo: photoManagerProviderRead.importedPhotos[index]);
              },
            ),
            if (isLoadingPhotos)
              PhotoAnalysisLoadingWidget(
                onCancel: () {
                  setState(() {
                    isLoadingPhotos = false;
                  });
                },
              ),
          ],
        ),
        floatingActionButton: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            FloatingActionButton(
              heroTag: 'analyzeAllPhotos',
              onPressed: () async {
                if (isLoadingPhotos) return;

                await analyzeAllPhotos();
              },
              child: const Icon(Icons.analytics),
            ),
            const SizedBox(width: 10),
            FloatingActionButton(
              heroTag: 'import',
              onPressed: () async {
                await Helper().getPhotosToImport(
                  context: context,
                  isAddLater: true,
                );

                setState(() {});
              },
              child: const Icon(Icons.add),
            ),
            const SizedBox(width: 10),
            FloatingActionButton(
              heroTag: 'importFinal',
              onPressed: () async {
                if (isLoadingPhotos) return;

                // eğer tarihi olamyan fotoğraf varsa uyarı ver. eğer onaylarsa tarihi şu an yap.
                if (photoManagerProviderRead.importedPhotos.any((element) => element.date == null)) {
                  await Helper().getDialog(
                    message: _noDateWarning,
                    onAccept: () async {
                      for (var element in photoManagerProviderRead.importedPhotos) {
                        element.date ??= DateTime.now();
                      }

                      Helper().getMessage(
                        message: _undatedPhotosSet,
                        status: StatusEnum.SUCCESS,
                      );

                      setState(() {});
                    },
                  );
                } else if (photoManagerProviderRead.importedPhotos.any((element) => !element.isAnalyzed)) {
                  await Helper().getDialog(
                    message: _mustAnalyze,
                    onAccept: () async {
                      await analyzeAllPhotos();
                    },
                  );
                } else {
                  // Final import için toplam fotoğraf sayısını ayarla
                  final List<Photo> photosToImport = context.read<PhotoManagerProvider>().importedPhotos;
                  setState(() {
                    photoAnalysisAllPhotosCount = photosToImport.length;
                    photoAnalysisProgressIndex.value = 0;
                    isLoadingPhotos = true;
                  });
                  await Helper().importPhotoFinal(context: context);
                  // Import tamamlandıktan sonra cache'i temizle
                  await clearPhotoCache();
                  setState(() {
                    isLoadingPhotos = false;
                  });
                }
              },
              child: const Icon(Icons.check),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> leavePage() async {
    final isSelectedAnyPhotos = context.read<PhotoManagerProvider>().selectedPhotos.isNotEmpty;

    if (isSelectedAnyPhotos) {
      context.read<PhotoManagerProvider>().selectedPhotos.clear();
      setState(() {});
    } else {
      if (isLoadingPhotos) {
        await Helper().cancelApproveDialog(context, isLoadingPhotos);
      } else {
        // Sayfadan çıkarken cache'i temizle
        await clearPhotoCache();
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> analyzeAllPhotos() async {
    // Sadece analiz edilmeyen fotoğrafları al
    final List<Photo> photosToProcess = photoManagerProviderRead.importedPhotos.where((photo) => !photo.isAnalyzed).toList();

    debugPrint('Total imported photos: ${photoManagerProviderRead.importedPhotos.length}');
    debugPrint('Photos to process: ${photosToProcess.length}');

    setState(() {
      photoAnalysisAllPhotosCount = photosToProcess.length; // Sadece işlenecek fotoğraf sayısını kullan
      photoAnalysisProgressIndex.value = 0;
      isLoadingPhotos = true;
    });

    // Eğer işlenecek fotoğraf yoksa, direkt çık
    if (photosToProcess.isEmpty) {
      debugPrint('No photos to process, exiting');
      setState(() {
        isLoadingPhotos = false;
      });
      return;
    }

    // Cache boyutunu kontrol et ve gerekirse temizle
    await manageCacheSize(maxSizeInMB: 300);

    // Create temporary directory for cropped photos
    Directory directory = await getApplicationDocumentsDirectory();
    Directory tempDirectory = Directory("${directory.path}/TempImportPhotos");
    if (await tempDirectory.exists()) {
      await tempDirectory.delete(recursive: true);
    }
    await tempDirectory.create(recursive: true);

    // Batch processing - process photos in small groups to prevent memory overflow
    int batchSize = calculateOptimalBatchSize(defaultBatchSize: 2, maxBatchSize: 3, minBatchSize: 1);

    for (int i = 0; i < photosToProcess.length; i += batchSize) {
      // Check if operation was cancelled
      if (!isLoadingPhotos) break;

      // Check memory safety before processing each batch
      if (!await isMemorySafeForProcessing()) {
        debugPrint('Memory warning: Reducing batch size');
        batchSize = (batchSize / 2).ceil().clamp(1, batchSize);
      }

      // Get batch of photos
      final int endIndex = (i + batchSize < photosToProcess.length) ? i + batchSize : photosToProcess.length;
      final List<Photo> batch = photosToProcess.sublist(i, endIndex);

      // Process each photo in the batch
      for (var photo in batch) {
        if (!isLoadingPhotos) break;

        try {
          // Check if file exists before processing
          final File originalFile = File(photo.path);
          if (!await originalFile.exists()) {
            photo.isAnalyzed = true;
            photoAnalysisProgressIndex.value++; // Sadece burada artır
            debugPrint('File not found, skipping: ${photo.path}');
            continue;
          }

          // Fotoğrafı cache'e kopyala (RAM yerine disk'te tut)
          String uniqueId = '${DateTime.now().millisecondsSinceEpoch}_${photo.hashCode}';
          String cachedPath = await cachePhoto(photo.path, uniqueId);

          // Analyze and crop the cached photo
          await photoDetectFace(photoPath: cachedPath);
          photo.face = currentFace;
          photo.totalAccuracy = totalAccuracy;
          photo.isAnalyzed = true;

          // Orijinal path'i cache path ile değiştir
          photo.path = cachedPath;

          photoAnalysisProgressIndex.value++; // Başarılı işlem sonunda artır
          debugPrint('Photo processed successfully: ${photoAnalysisProgressIndex.value}/$photoAnalysisAllPhotosCount');
        } catch (e) {
          debugPrint('Error processing photo: ${photo.path}, Error: $e');
          photo.isAnalyzed = true; // Mark as analyzed even if failed to prevent infinite loop
          photoAnalysisProgressIndex.value++; // Hata durumunda da artır
        }
      }

      // Force garbage collection between batches to free memory
      forceGarbageCollection();
      await Future.delayed(const Duration(milliseconds: 200));
    }

    if (mounted) {
      setState(() {
        isLoadingPhotos = false;
      });
    }
  }
}
